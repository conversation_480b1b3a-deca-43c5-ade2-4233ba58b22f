<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "Updating document file paths...\n";
    
    // Update the test documents to point to actual files
    $updates = [
        [1, 'uploads/documents/user_1/drivers_license.html'],
        [2, 'uploads/documents/user_1/drivers_license.html'], // Same file for testing
        [3, 'uploads/documents/user_1/drivers_license.html'], // Same file for testing
        [4, 'uploads/documents/user_1/drivers_license.html']  // Same file for testing
    ];
    
    foreach ($updates as [$id, $path]) {
        $result = $db->query("UPDATE user_documents SET file_path = ? WHERE id = ?", [$path, $id]);
        if ($result) {
            echo "✓ Updated document ID $id with path: $path\n";
        } else {
            echo "✗ Failed to update document ID $id\n";
        }
    }
    
    // Verify the updates
    $result = $db->query("SELECT id, document_name, file_path FROM user_documents");
    echo "\nCurrent documents:\n";
    while ($row = $result->fetch_assoc()) {
        echo "ID: {$row['id']}, Name: {$row['document_name']}, Path: {$row['file_path']}\n";
    }
    
    echo "\nDocument paths updated successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
