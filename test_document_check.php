<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    // Check if user_documents table exists and has data
    $result = $db->query("SELECT COUNT(*) as count FROM user_documents");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "User documents found: " . $count . "\n";
        
        // Get a sample document if exists
        if ($count > 0) {
            $sample = $db->query("SELECT id, document_type, document_name, file_path FROM user_documents LIMIT 1");
            if ($sample && $sample->num_rows > 0) {
                $doc = $sample->fetch_assoc();
                echo "Sample document:\n";
                echo "  ID: " . $doc['id'] . "\n";
                echo "  Type: " . $doc['document_type'] . "\n";
                echo "  Name: " . $doc['document_name'] . "\n";
                echo "  Path: " . $doc['file_path'] . "\n";
            }
        }
        
        // Test the AJAX endpoint
        echo "\nTesting AJAX endpoint...\n";
        if ($count > 0) {
            $first_doc = $db->query("SELECT id FROM user_documents LIMIT 1");
            if ($first_doc && $first_doc->num_rows > 0) {
                $doc_id = $first_doc->fetch_assoc()['id'];
                echo "Testing with document ID: " . $doc_id . "\n";
                
                // Simulate the AJAX call
                $_GET['id'] = $doc_id;
                ob_start();
                include 'admin/ajax/get_user_document_details.php';
                $response = ob_get_clean();
                echo "AJAX response: " . $response . "\n";
            }
        } else {
            echo "No documents to test with.\n";
        }
        
    } else {
        echo "Error: Could not query user_documents table\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
