<?php
/**
 * Comprehensive Test for View User Functionality
 * Tests all components, functions, and links in the view-user page
 */

// Set error reporting to show all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to capture any errors
ob_start();

echo "<!DOCTYPE html>\n";
echo "<html lang='en'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>View User Functionality Test</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo ".success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }\n";
echo ".error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }\n";
echo ".warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }\n";
echo ".info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }\n";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<h1>🧪 View User Functionality Test Suite</h1>\n";
echo "<p><strong>Test Date:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    echo "<div class='test-section'>\n";
    echo "<h3>🔍 Testing: {$test_name}</h3>\n";
    
    try {
        $result = $test_function();
        if ($result['success']) {
            echo "<div class='success'>✅ PASSED: {$result['message']}</div>\n";
            $passed_tests++;
            $test_results[$test_name] = 'PASSED';
        } else {
            echo "<div class='error'>❌ FAILED: {$result['message']}</div>\n";
            $test_results[$test_name] = 'FAILED';
        }
        
        if (isset($result['details'])) {
            echo "<pre>{$result['details']}</pre>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ ERROR: {$e->getMessage()}</div>\n";
        $test_results[$test_name] = 'ERROR';
    }
    
    echo "</div>\n";
}

// Test 1: Check if config files exist and are accessible
runTest("Configuration Files", function() {
    $config_files = [
        '../config/config.php',
        '../config/database.php',
        '../config/email.php'
    ];
    
    $missing_files = [];
    foreach ($config_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        }
    }
    
    if (empty($missing_files)) {
        return ['success' => true, 'message' => 'All configuration files exist'];
    } else {
        return ['success' => false, 'message' => 'Missing files: ' . implode(', ', $missing_files)];
    }
});

// Test 2: Check if component files exist
runTest("Component Files", function() {
    $component_files = [
        '../includes/components/user-header.php',
        '../includes/components/user-overview-cards.php',
        '../includes/components/user-personal-info.php',
        '../includes/components/user-account-info.php',
        '../includes/components/user-security-section.php',
        '../includes/components/user-financial-section.php',
        '../includes/components/user-cards-crypto-section.php',
        '../includes/components/user-modals.php'
    ];
    
    $missing_files = [];
    $file_sizes = [];
    
    foreach ($component_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        } else {
            $file_sizes[] = basename($file) . ': ' . filesize($file) . ' bytes';
        }
    }
    
    if (empty($missing_files)) {
        return [
            'success' => true, 
            'message' => 'All component files exist',
            'details' => implode("\n", $file_sizes)
        ];
    } else {
        return ['success' => false, 'message' => 'Missing files: ' . implode(', ', $missing_files)];
    }
});

// Test 3: Check if CSS and JS files exist
runTest("Asset Files", function() {
    $asset_files = [
        '../assets/admin/css/view-user.css',
        '../assets/admin/js/view-user.js'
    ];
    
    $missing_files = [];
    $file_info = [];
    
    foreach ($asset_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        } else {
            $size = filesize($file);
            $lines = count(file($file));
            $file_info[] = basename($file) . ": {$size} bytes, {$lines} lines";
        }
    }
    
    if (empty($missing_files)) {
        return [
            'success' => true, 
            'message' => 'All asset files exist',
            'details' => implode("\n", $file_info)
        ];
    } else {
        return ['success' => false, 'message' => 'Missing files: ' . implode(', ', $missing_files)];
    }
});

// Test 4: Check if data loader file exists and has proper structure
runTest("Data Loader", function() {
    $file = '../admin/includes/user-data-loader.php';
    
    if (!file_exists($file)) {
        return ['success' => false, 'message' => 'Data loader file does not exist'];
    }
    
    $content = file_get_contents($file);
    $required_variables = [
        '$user', '$user_stats', '$recent_transactions', '$virtual_cards', 
        '$crypto_accounts', '$otp_history', '$login_attempts'
    ];
    
    $missing_vars = [];
    foreach ($required_variables as $var) {
        if (strpos($content, $var) === false) {
            $missing_vars[] = $var;
        }
    }
    
    if (empty($missing_vars)) {
        return [
            'success' => true, 
            'message' => 'Data loader has all required variables',
            'details' => 'File size: ' . filesize($file) . ' bytes'
        ];
    } else {
        return ['success' => false, 'message' => 'Missing variables: ' . implode(', ', $missing_vars)];
    }
});

// Test 5: Check CSS syntax
runTest("CSS Syntax", function() {
    $css_file = '../assets/admin/css/view-user.css';
    
    if (!file_exists($css_file)) {
        return ['success' => false, 'message' => 'CSS file does not exist'];
    }
    
    $content = file_get_contents($css_file);
    
    // Basic CSS syntax checks
    $open_braces = substr_count($content, '{');
    $close_braces = substr_count($content, '}');
    
    if ($open_braces !== $close_braces) {
        return ['success' => false, 'message' => "CSS syntax error: Mismatched braces ({$open_braces} open, {$close_braces} close)"];
    }
    
    // Check for required CSS classes
    $required_classes = ['.credit-card', '.fade-in', '.loading-spinner'];
    $missing_classes = [];
    
    foreach ($required_classes as $class) {
        if (strpos($content, $class) === false) {
            $missing_classes[] = $class;
        }
    }
    
    if (empty($missing_classes)) {
        return [
            'success' => true, 
            'message' => 'CSS syntax is valid and contains required classes',
            'details' => "Braces: {$open_braces} pairs\nFile size: " . strlen($content) . " characters"
        ];
    } else {
        return ['success' => false, 'message' => 'Missing CSS classes: ' . implode(', ', $missing_classes)];
    }
});

// Test 6: Check JavaScript syntax
runTest("JavaScript Syntax", function() {
    $js_file = '../assets/admin/js/view-user.js';
    
    if (!file_exists($js_file)) {
        return ['success' => false, 'message' => 'JavaScript file does not exist'];
    }
    
    $content = file_get_contents($js_file);
    
    // Check for required functions
    $required_functions = ['generateOTPForUser', 'showOTPHistory', 'initializePageAnimations'];
    $missing_functions = [];
    
    foreach ($required_functions as $func) {
        if (strpos($content, "function {$func}") === false && strpos($content, "{$func}:") === false) {
            $missing_functions[] = $func;
        }
    }
    
    if (empty($missing_functions)) {
        return [
            'success' => true, 
            'message' => 'JavaScript contains all required functions',
            'details' => "File size: " . strlen($content) . " characters"
        ];
    } else {
        return ['success' => false, 'message' => 'Missing functions: ' . implode(', ', $missing_functions)];
    }
});

// Test 7: Check if new view-user file exists
runTest("New View User File", function() {
    $file = '../admin/view-user-new.php';
    
    if (!file_exists($file)) {
        return ['success' => false, 'message' => 'New view-user file does not exist'];
    }
    
    $content = file_get_contents($file);
    $lines = count(file($file));
    
    // Check if it includes components
    $required_includes = ['user-header.php', 'user-overview-cards.php', 'view-user.css', 'view-user.js'];
    $missing_includes = [];
    
    foreach ($required_includes as $include) {
        if (strpos($content, $include) === false) {
            $missing_includes[] = $include;
        }
    }
    
    if (empty($missing_includes)) {
        return [
            'success' => true, 
            'message' => 'New view-user file includes all required components',
            'details' => "Lines: {$lines}\nFile size: " . filesize($file) . " bytes"
        ];
    } else {
        return ['success' => false, 'message' => 'Missing includes: ' . implode(', ', $missing_includes)];
    }
});

// Display summary
echo "<div class='test-section info'>\n";
echo "<h2>📊 Test Summary</h2>\n";
echo "<p><strong>Total Tests:</strong> {$total_tests}</p>\n";
echo "<p><strong>Passed:</strong> {$passed_tests}</p>\n";
echo "<p><strong>Failed:</strong> " . ($total_tests - $passed_tests) . "</p>\n";
echo "<p><strong>Success Rate:</strong> " . round(($passed_tests / $total_tests) * 100, 2) . "%</p>\n";

if ($passed_tests === $total_tests) {
    echo "<div class='success'><strong>🎉 ALL TESTS PASSED!</strong> The view-user functionality is working correctly.</div>\n";
} else {
    echo "<div class='warning'><strong>⚠️ SOME TESTS FAILED</strong> Please review the failed tests above.</div>\n";
}

echo "</div>\n";

// Display detailed results
echo "<div class='test-section'>\n";
echo "<h3>📋 Detailed Results</h3>\n";
echo "<pre>\n";
foreach ($test_results as $test => $result) {
    $icon = $result === 'PASSED' ? '✅' : ($result === 'FAILED' ? '❌' : '⚠️');
    echo "{$icon} {$test}: {$result}\n";
}
echo "</pre>\n";
echo "</div>\n";

echo "</body>\n";
echo "</html>\n";

// Get any buffered output
$output = ob_get_clean();
echo $output;
?>
